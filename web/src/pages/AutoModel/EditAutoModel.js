import React, { useEffect, useState } from 'react';
import {
  API,
  showError,
  showSuccess,
  showInfo,
  getModelCategories,
} from '../../helpers';
import { copy } from '../../helpers/utils';
import {
  Button,
  SideSheet,
  Space,
  Spin,
  Typography,
  Form,
  Input,
  Switch,
  Select,
  Divider,
  Card,
  Avatar,
  Col,
  Row,
} from '@douyinfe/semi-ui';
import {
  IconSave,
  IconClose,
  IconCode,
} from '@douyinfe/semi-icons';
import { useTranslation } from 'react-i18next';

const { Text, Title } = Typography;

const EditAutoModel = ({ refresh, editingAutoModel, visiable, handleClose }) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [formApi, setFormApi] = useState(null);
  const [models, setModels] = useState([]);
  const [allModels, setAllModels] = useState([]);
  const [customModel, setCustomModel] = useState('');

  const isEdit = editingAutoModel.id !== undefined;

  const initFormValues = {
    name: '',
    description: '',
    is_active: true,
    models: [],
  };

  const loadModels = async () => {
    let res = await API.get(`/api/user/models`);
    const { success, message, data } = res.data;
    if (success) {
      const categories = getModelCategories(t);
      let localModelOptions = data.map((model) => {
        let icon = null;
        for (const [key, category] of Object.entries(categories)) {
          if (key !== 'all' && category.filter({ model_name: model })) {
            icon = category.icon;
            break;
          }
        }
        return {
          label: (
            <span className="flex items-center gap-1">
              {icon}
              {model}
            </span>
          ),
          value: model,
        };
      });
      setModels(localModelOptions);
      setAllModels(data); // 保存所有模型名称的数组
    } else {
      showError(t(message));
    }
  };

  const addCustomModels = () => {
    if (customModel.trim() === '') return;
    const modelArray = customModel.split(',').map((model) => model.trim());

    const currentModels = formApi?.getValue('models') || [];
    let localModels = [...currentModels];
    let localModelOptions = [...models];
    const addedModels = [];

    modelArray.forEach((model) => {
      if (model && !localModels.includes(model)) {
        localModels.push(model);
        localModelOptions.push({
          key: model,
          label: model,
          value: model,
        });
        addedModels.push(model);
      }
    });

    setModels(localModelOptions);
    setCustomModel('');
    formApi?.setValue('models', localModels);

    if (addedModels.length > 0) {
      showSuccess(
        t('已新增 {{count}} 个模型：{{list}}', {
          count: addedModels.length,
          list: addedModels.join(', '),
        })
      );
    } else {
      showInfo(t('未发现新增模型'));
    }
  };

  const handleModelChange = (value) => {
    formApi?.setValue('models', value);
  };

  useEffect(() => {
    if (visiable) {
      loadModels();
      if (isEdit) {
        setLoading(true);
        // 如果是编辑模式，设置表单值
        if (formApi) {
          formApi.setValues({
            name: editingAutoModel.name || '',
            description: editingAutoModel.description || '',
            is_active: editingAutoModel.is_active !== false,
            models: editingAutoModel.models || [],
          });
        }
        setLoading(false);
      } else {
        // 如果是新建模式，重置表单
        if (formApi) {
          formApi.setValues(initFormValues);
        }
      }
    }
  }, [visiable, isEdit, editingAutoModel, formApi]);

  const submit = async (values) => {
    console.log('Form submission started with values:', values);
    setLoading(true);

    try {
      // Validate required fields
      if (!values.name || !values.name.trim()) {
        showError(t('请输入名称'));
        setLoading(false);
        return;
      }

      if (!values.models || values.models.length === 0) {
        showError(t('请选择至少一个模型'));
        setLoading(false);
        return;
      }

      let res;
      const payload = {
        name: values.name.trim(),
        description: values.description || '',
        is_active: values.is_active !== false, // Default to true if undefined
        models: values.models || [],
      };

      console.log('Submitting payload:', payload);

      if (isEdit) {
        // 更新现有配置
        console.log('Updating existing config:', editingAutoModel.name);
        res = await API.put(`/api/auto_models/${editingAutoModel.name}`, payload);
      } else {
        // 创建新配置
        console.log('Creating new config');
        res = await API.post('/api/auto_models', payload);
      }

      console.log('API response:', res);
      const { success, message } = res.data;
      if (success) {
        showSuccess(isEdit ? t('模型负载更新成功！') : t('模型负载创建成功！'));
        handleClose();
        refresh();
      } else {
        console.error('API returned error:', message);
        showError(message);
      }
    } catch (error) {
      console.error('Form submission error:', error);
      if (error.response) {
        console.error('Error response:', error.response);
        const errorMessage = error.response.data?.message || error.message;
        showError(errorMessage);
      } else if (error.request) {
        console.error('Network error - no response received:', error.request);
        showError(t('网络请求失败，请检查网络连接'));
      } else {
        console.error('Request setup error:', error.message);
        showError(error.message);
      }
    } finally {
      console.log('Form submission completed, setting loading to false');
      setLoading(false);
    }
  };

  return (
    <SideSheet
      title={
        <div className="flex items-center">
          <Title heading={6} style={{ margin: 0 }}>
            {isEdit ? t('编辑模型负载') : t('创建模型负载')}
          </Title>
        </div>
      }
      visible={visiable}
      onCancel={handleClose}
      width={600}
      bodyStyle={{ padding: 0 }}
      headerStyle={{ padding: '20px 24px 0' }}
      closeIcon={<IconClose />}
    >
      <Spin spinning={loading}>
        <div className="p-6">
          <Form
            initValues={initFormValues}
            getFormApi={(api) => setFormApi(api)}
            onSubmit={submit}
            labelPosition="top"
            style={{ width: '100%' }}
          >
            <Form.Input
              field="name"
              label={t('名称')}
              placeholder={t('请输入模型负载名称')}
              rules={[
                { required: true, message: t('请输入名称') },
                { 
                  pattern: /^[a-zA-Z0-9_-]+$/, 
                  message: t('名称只能包含字母、数字、下划线和连字符') 
                }
              ]}
              disabled={isEdit} // 编辑时不允许修改名称
            />

            <Form.Input
              field="description"
              label={t('描述')}
              placeholder={t('请输入描述信息（可选）')}
            />

            <Form.Switch
              field="is_active"
              label={t('是否启用')}
              checkedText={t('开')}
              uncheckedText={t('关')}
            />

            <Form.Select
              field='models'
              label={t('模型')}
              placeholder={t('请选择模型')}
              rules={[{ required: true, message: t('请选择模型') }]}
              multiple
              filter
              searchPosition='dropdown'
              optionList={models}
              style={{ width: '100%' }}
              onChange={handleModelChange}
              extraText={(
                <Space wrap>
                  <Button size='small' type='secondary' onClick={() => handleModelChange(allModels)}>
                    {t('填入所有模型')}
                  </Button>
                  <Button size='small' type='warning' onClick={() => handleModelChange([])}>
                    {t('清除所有模型')}
                  </Button>
                  <Button
                    size='small'
                    type='tertiary'
                    onClick={() => {
                      const currentModels = formApi?.getValue('models') || [];
                      if (currentModels.length === 0) {
                        showInfo(t('没有模型可以复制'));
                        return;
                      }
                      try {
                        copy(currentModels.join(','));
                        showSuccess(t('模型列表已复制到剪贴板'));
                      } catch (error) {
                        showError(t('复制失败'));
                      }
                    }}
                  >
                    {t('复制所有模型')}
                  </Button>
                </Space>
              )}
            />

            <Form.Input
              field='custom_model'
              label={t('自定义模型名称')}
              placeholder={t('输入自定义模型名称')}
              onChange={(value) => setCustomModel(value.trim())}
              value={customModel}
              suffix={
                <Button size='small' type='primary' onClick={addCustomModels}>
                  {t('填入')}
                </Button>
              }
            />

            <Divider />

            <div className="flex justify-end gap-3">
              <Button onClick={handleClose}>
                {t('取消')}
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                icon={<IconSave />}
              >
                {isEdit ? t('更新') : t('提交')}
              </Button>
            </div>
          </Form>
        </div>
      </Spin>
    </SideSheet>
  );
};

export default EditAutoModel;
